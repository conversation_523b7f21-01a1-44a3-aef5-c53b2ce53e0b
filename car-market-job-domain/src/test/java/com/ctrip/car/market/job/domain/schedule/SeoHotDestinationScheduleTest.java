package com.ctrip.car.market.job.domain.schedule;


import com.ctrip.car.market.job.domain.dto.*;
import com.ctrip.car.market.job.domain.proxy.IGTBasicServiceProxy;
import com.ctrip.car.market.job.domain.proxy.OsdBasicDataProxy;
import com.ctrip.car.market.job.domain.proxy.TourAIOneServiceClientForGroup;
import com.ctrip.car.market.job.domain.proxy.IgtGeoServiceProxy;
import com.ctrip.car.market.job.domain.proxy.GlobalPoiJavaProxy;
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.repository.entity.*;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.CountryRepository;
import com.ctrip.dcs.geo.domain.repository.ProvinceRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.dcs.geo.domain.value.Province;
import com.ctrip.gs.globalpoi.soa.contract.GlobalContent;
import com.ctrip.igt.basicservice.interfaces.dto.BasicAirportDTO;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.ctrip.gs.globalpoi.soa.contract.GlobalInfo;
import com.ctrip.tour.ai.one.service.TourAIOneServiceClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;
import qunar.tc.qconfig.client.MapConfig;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.MessageSendStateListener;

import java.sql.SQLException;
import java.util.*;

import static org.mockito.Mockito.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest({
        CLogUtil.class,
        TourAIOneServiceClient.class,
        TourAIOneServiceClientForGroup.class,
        MapConfig.class,
        QConfigUtil.class
})
@SuppressStaticInitializationFor({
        "com.ctrip.car.market.job.domain.utils.CLogUtil",
        "com.ctrip.tour.ai.one.service.TourAIOneServiceClient",
        "com.ctrip.car.market.job.domain.proxy.TourAIOneServiceClientForGroup",
        "qunar.tc.qconfig.client.MapConfig",
        "com.ctrip.car.market.job.domain.utils.QConfigUtil"
})
public class SeoHotDestinationScheduleTest {

    @Mock
    MessageProducer messageProducer;
    @Mock
    SeoHotDestinationBusiness seoHotDestinationBusiness;
    @Mock
    CountryRepository countryRepository;
    @Mock
    CityRepository cityRepository;
    @Mock
    IGTBasicServiceProxy igtBasicServiceProxy;
    @Mock
    OsdBasicDataProxy osdBasicDataProxy;
    @Mock
    ProvinceRepository provinceRepository;
    @Mock
    IgtGeoServiceProxy igtGeoServiceProxy;
    @Mock
    GlobalPoiJavaProxy globalPoiJavaProxy;
    @InjectMocks
    SeoHotDestinationSchedule seoHotDestinationSchedule;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(CLogUtil.class);
        PowerMockito.mockStatic(QConfigUtil.class);
        PowerMockito.mockStatic(TourAIOneServiceClientForGroup.class);
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("pull.batch.size"),eq("500"))).thenReturn("500");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("sql.batch.size"),eq("200"))).thenReturn("200");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("sql.query.batch.size"),eq("500"))).thenReturn("500");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("seo.ibu.qmq.locale"),eq("en-ID,en-PH,en-TH,en-IL,nl-NL,pt-BR,tr-TR,NL-BE,pt-PT,de-DE,fr-FR,th-TH,fr-BE,de-AT,de-CH,fr-CH,es-US,es-MX,en-AE,en-AU,en-MY,en-NZ,en-SA,en-SG,es-ES,it-IT,en-MY,en-BE,en-IE,en-CA,en-GB,en-HK,en-US,en-XX,ja-JP,ko-KR,zh-HK,zh-TW"))).thenReturn("en-ID");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("seo.ibu.qmq.pre.url"),eq("https://www.trip.com/carhire/"))).thenReturn("https://www.trip.com/carhire/");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("twCityList"),eq("617,720,3845,3847,3848,3849,5152,5589,6954,7203,7523,7524,7570,7614,7662,7805,7808,7809,7810,7811,650358,669328"))).thenReturn("617,720,3845,3847,3848,3849,5152,5589,6954,7203,7523,7524,7570,7614,7662,7805,7808,7809,7810,7811,650358,669328");

    }

    @Test
    public void testPullFromBi2() throws Exception {
        when(igtBasicServiceProxy.basicAirportQuery(anyListOf(String.class))).thenReturn(new ArrayList<BasicAirportDTO>());
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(anyBoolean())).thenThrow(new SQLException());
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(anyBoolean())).thenReturn(Collections.singletonList(new SeoHotCountryinfo()));
        when(seoHotDestinationBusiness.getSeoHotDestinationinfoList(anyBoolean())).thenReturn(Collections.singletonList(new SeoHotDestinatioinfo()));
        when(countryRepository.findMany(anyListOf(Long.class))).thenReturn(new HashMap<Long, Country>() {{
            put(1L, new Country());
        }});
        when(cityRepository.findMany(anyListOf(Long.class))).thenReturn(new HashMap<Long, City>() {{
            put(1L, new City());
        }});
        seoHotDestinationSchedule.pullFromBi();
    }

    @Test
    public void testPushToIbu() throws SQLException {
        List<SeoHotCityinfo> seoHotCityinfoList = new ArrayList<>();
        List<SeoHotDestinatioinfo> seoHotDestinationinfoList = new ArrayList<>();
        List<SeoHotDestinatioinfo> seoScenerySpotList = new ArrayList<>();
        List<SeoHotDestinatioinfo> seoTrainStationList = new ArrayList<>();
        List<SeoHotCountryinfo> seoHotCountryinfoList = new ArrayList<>();
        when(seoHotDestinationBusiness.getSeoHotDestinationinfoList(Boolean.TRUE)).thenReturn(seoHotDestinationinfoList);
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(Boolean.TRUE)).thenReturn(seoHotCityinfoList);
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(Boolean.TRUE)).thenReturn(seoHotCountryinfoList);
        when(seoHotDestinationBusiness.getSeoScenerySpotList(Boolean.TRUE)).thenReturn(seoScenerySpotList);
        when(seoHotDestinationBusiness.getSeoTrainStationList(Boolean.TRUE)).thenReturn(seoTrainStationList);
        when(seoHotDestinationBusiness.buildHotDestinationMassageDTO(any(MessageParams.class))).thenReturn(Collections.singletonList(new HotDestinationMassageDTO()));
        when(messageProducer.generateMessage(anyString())).thenReturn(null);
        seoHotDestinationSchedule.pushToIbu();
    }

    @Test
    public void testPushToIbu1() throws SQLException {
        List<SeoHotCityinfo> seoHotCityinfoList = new ArrayList<>();
        List<SeoHotCountryinfo> seoHotCountryinfoList = new ArrayList<>();
        when(seoHotDestinationBusiness.getSeoHotDestinationinfoList(Boolean.TRUE)).thenThrow( new SQLException());
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(Boolean.TRUE)).thenReturn(seoHotCityinfoList);
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(Boolean.TRUE)).thenReturn(seoHotCountryinfoList);
        when(seoHotDestinationBusiness.buildHotDestinationMassageDTO(any(MessageParams.class))).thenReturn(Collections.singletonList(new HotDestinationMassageDTO()));
        when(messageProducer.generateMessage(anyString())).thenReturn(null);
        seoHotDestinationSchedule.pushToIbu();
    }

    @Test
    public void testPushToIbu2() throws SQLException {
        List<SeoHotCityinfo> seoHotCityinfoList = new ArrayList<>();
        seoHotCityinfoList.add(mock(SeoHotCityinfo.class));
        List<SeoHotDestinatioinfo> seoHotDestinationinfoList = new ArrayList<>();
        seoHotDestinationinfoList.add(mock(SeoHotDestinatioinfo.class));
        List<SeoHotCountryinfo> seoHotCountryinfoList = new ArrayList<>();
        seoHotCountryinfoList.add(mock(SeoHotCountryinfo.class));
        when(seoHotDestinationBusiness.getSeoHotDestinationinfoList(Boolean.TRUE)).thenReturn(seoHotDestinationinfoList);
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(Boolean.TRUE)).thenReturn(seoHotCityinfoList);
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(Boolean.TRUE)).thenReturn(seoHotCountryinfoList);
        when(seoHotDestinationBusiness.buildHotDestinationMassageDTO(any(MessageParams.class))).thenReturn(Collections.singletonList(new HotDestinationMassageDTO()));
        when(messageProducer.generateMessage(anyString())).thenReturn(null);
        seoHotDestinationSchedule.pushToIbu();
    }
    @Test
    public void testPushToIbu3() throws SQLException {
        List<SeoHotCityinfo> seoHotCityinfoList = new ArrayList<>();
        seoHotCityinfoList.add(mock(SeoHotCityinfo.class));
        List<SeoHotDestinatioinfo> seoScenerySpotList = new ArrayList<>();
        seoScenerySpotList.add(mock(SeoHotDestinatioinfo.class));
        List<SeoHotCountryinfo> seoHotCountryinfoList = new ArrayList<>();
        seoHotCountryinfoList.add(mock(SeoHotCountryinfo.class));
        when(seoHotDestinationBusiness.getSeoScenerySpotList(Boolean.TRUE)).thenReturn(seoScenerySpotList);
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(Boolean.TRUE)).thenReturn(seoHotCityinfoList);
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(Boolean.TRUE)).thenReturn(seoHotCountryinfoList);
        when(seoHotDestinationBusiness.buildHotDestinationMassageDTO(any(MessageParams.class))).thenReturn(Collections.singletonList(new HotDestinationMassageDTO()));
        when(messageProducer.generateMessage(anyString())).thenReturn(null);
        seoHotDestinationSchedule.pushToIbu();
    }
    @Test
    public void testPushToIbu4() throws SQLException {
        List<SeoHotCityinfo> seoHotCityinfoList = new ArrayList<>();
        seoHotCityinfoList.add(mock(SeoHotCityinfo.class));
        List<SeoHotDestinatioinfo> seoTrainStationList = new ArrayList<>();
        seoTrainStationList.add(mock(SeoHotDestinatioinfo.class));
        List<SeoHotCountryinfo> seoHotCountryinfoList = new ArrayList<>();
        seoHotCountryinfoList.add(mock(SeoHotCountryinfo.class));
        when(seoHotDestinationBusiness.getSeoTrainStationList(Boolean.TRUE)).thenReturn(seoTrainStationList);
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(Boolean.TRUE)).thenReturn(seoHotCityinfoList);
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(Boolean.TRUE)).thenReturn(seoHotCountryinfoList);
        when(seoHotDestinationBusiness.buildHotDestinationMassageDTO(any(MessageParams.class))).thenReturn(Collections.singletonList(new HotDestinationMassageDTO()));
        when(messageProducer.generateMessage(anyString())).thenReturn(null);
        seoHotDestinationSchedule.pushToIbu();
    }
    @Test
    public void testSendMassage() {
        HotDestinationMassageDTO hotDestinationMassageDTO = new HotDestinationMassageDTO();
        hotDestinationMassageDTO.setIsHot(0);
        when(messageProducer.generateMessage(anyString())).thenReturn(mock(Message.class));
        messageProducer.sendMessage(any(Message.class), any(MessageSendStateListener.class));
        ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "sendMassage", hotDestinationMassageDTO);
        Assert.assertEquals(hotDestinationMassageDTO.getIsHot().toString(), "0");
    }

    @Test
    public void testSaveDestination() throws Exception {
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("pull.batch.size"), eq("500"))).thenReturn("500");
        SeoHotCityinfo seoHotCityinfo1 = new SeoHotCityinfo() {{
            setCityId(1);
            setCityName("shanghai");
            setCountryId(1);
            setStatus(0);
        }};
        SeoHotCityinfo seoHotCityinfo2 = new SeoHotCityinfo() {{
            setCityId(189);
            setCityName("shanghai");
            setCountryId(89);
            setStatus(0);
        }};
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(anyBoolean())).thenReturn(Arrays.asList(seoHotCityinfo1, seoHotCityinfo2));
        SeoHotCountryinfo seoHotCountryinfo = new SeoHotCountryinfo() {{
            setCountryId(1);
            setStatus(0);
            setCountryName("china");
        }};
        SeoHotCountryinfo seoHotCountryinfo2 = new SeoHotCountryinfo() {{
            setCountryId(3);
            setStatus(0);
            setCountryName("china1");
        }};
        SeoHotCountryinfo seoHotCountryinfo3 = new SeoHotCountryinfo() {{
            setCountryId(89);
            setStatus(0);
            setCountryName("china1");
        }};
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(anyBoolean())).thenReturn(Arrays.asList(seoHotCountryinfo2, seoHotCountryinfo, seoHotCountryinfo3));
        when(seoHotDestinationBusiness.getSeoHotDestinationinfoList(anyBoolean())).thenReturn(Collections.singletonList(new SeoHotDestinatioinfo() {{
            setPoiCode("CODE");
            setStatus(0);
            setCountryId(89);
            setCityId(1);
        }}));
        when(countryRepository.findMany(anyListOf(Long.class))).thenReturn(new HashMap<Long, Country>() {{
            put(1L, new Country());
        }});
        when(cityRepository.findMany(anyListOf(Long.class))).thenReturn(new HashMap<Long, City>() {{
            put(1L, City.builder().translationName("shanghai").build());
        }});
        GetHostDestinationInfo getHostDestinationInfo = new GetHostDestinationInfo();
        getHostDestinationInfo.setPickuplocationcode("CODE");
        getHostDestinationInfo.setPickupcityid("1");
        getHostDestinationInfo.setPickupcountryid("1");
        GetHostDestinationInfo getHostDestinationInfo2 = new GetHostDestinationInfo();
        getHostDestinationInfo2.setPickuplocationcode("CODE12");
        getHostDestinationInfo2.setPickupcityid("3");
        getHostDestinationInfo2.setPickupcountryid("3");

        GetHostDestinationInfo getHostDestinationInfo3 = new GetHostDestinationInfo();
        getHostDestinationInfo3.setPickuplocationcode("31231");
        getHostDestinationInfo3.setPickupcityid("189");
        getHostDestinationInfo3.setPickupcountryid("89");


        List<com.ctrip.car.osd.basicdataservice.dto.Airport> airports = new ArrayList<>();
        com.ctrip.car.osd.basicdataservice.dto.Airport airport = new com.ctrip.car.osd.basicdataservice.dto.Airport();
        airport.setCityId(1);
        airports.add(airport);
        List<com.ctrip.car.osd.basicdataservice.dto.Airport> airports2 = new ArrayList<>();
        com.ctrip.car.osd.basicdataservice.dto.Airport airport2 = new com.ctrip.car.osd.basicdataservice.dto.Airport();
        airport2.setCityId(33);
        airports2.add(airport2);
        when(osdBasicDataProxy.getAirportNoCityId("CODE", "en-US")).thenReturn(airports);
        when(osdBasicDataProxy.getAirportNoCityId("CODE12", "en-US")).thenReturn(airports2);
        seoHotDestinationSchedule.saveDestination(Arrays.asList(getHostDestinationInfo, getHostDestinationInfo2));
        seoHotDestinationSchedule.saveDestination(Arrays.asList(getHostDestinationInfo, getHostDestinationInfo));
        seoHotDestinationSchedule.saveDestination(Arrays.asList(getHostDestinationInfo, getHostDestinationInfo3));
        Assert.assertEquals(getHostDestinationInfo2.getPickupcityid(), "33");
        Assert.assertEquals(getHostDestinationInfo.getPickupcityid(), "1");
        Assert.assertEquals(getHostDestinationInfo3.getPickupcityid(), "189");
    }
    @Test
    public void testSaveDestination1() throws Exception {
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("pull.batch.size"), eq("500"))).thenReturn("500");

        List<SeoHotCityinfo> cityinfoList = new ArrayList<>();
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(anyBoolean())).thenReturn(cityinfoList);
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(anyBoolean())).thenReturn(new ArrayList<>());
        when(seoHotDestinationBusiness.getSeoHotDestinationinfoList(anyBoolean())).thenReturn(new ArrayList<>());
        when(countryRepository.findMany(anyListOf(Long.class))).thenReturn(new HashMap<Long, Country>() {{
            put(1L, new Country());
        }});
        when(cityRepository.findMany(anyListOf(Long.class))).thenReturn(new HashMap<Long, City>() {{
            put(1L, City.builder().translationName("shanghai").build());
        }});
        GetHostDestinationInfo getHostDestinationInfo1 = new GetHostDestinationInfo();
        getHostDestinationInfo1.setPickuplocationcode("CODE");
        getHostDestinationInfo1.setPickupcityid("1");
        getHostDestinationInfo1.setPickupcountryid("1");

        List<com.ctrip.car.osd.basicdataservice.dto.Airport> airports = new ArrayList<>();
        com.ctrip.car.osd.basicdataservice.dto.Airport airport = new com.ctrip.car.osd.basicdataservice.dto.Airport();
        airport.setCityId(1);
        airports.add(airport);

        when(osdBasicDataProxy.getAirportNoCityId(anyString(), anyString())).thenReturn(airports);
        seoHotDestinationSchedule.saveDestination(Collections.singletonList(getHostDestinationInfo1));
        Assert.assertEquals(getHostDestinationInfo1.getPickupcityid(), "1");
    }

    @Test
    public void testSaveDestination2() throws Exception {
        List<SeoHotCityinfo> cityinfoList = new ArrayList<>();
        cityinfoList.add(new SeoHotCityinfo(){{setCityId(2);setStatus(1);setCityName("shanghai");setCountryId(1);}});
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(anyBoolean())).thenReturn(cityinfoList);
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(anyBoolean())).thenReturn(Collections.singletonList(new SeoHotCountryinfo(){{setCountryId(1);setStatus(1);setCountryName("china");}}));
        when(seoHotDestinationBusiness.getSeoHotDestinationinfoList(anyBoolean())).thenReturn(Collections.singletonList(new SeoHotDestinatioinfo(){{setPoiCode("aaaa");setStatus(1);}}));
        when(countryRepository.findMany(anyListOf(Long.class))).thenReturn(new HashMap<Long, Country>() {{
            put(1L, new Country());
        }});
        when(cityRepository.findMany(anyListOf(Long.class))).thenReturn(new HashMap<Long, City>() {{
            put(1L, new City());
        }});
        when(igtBasicServiceProxy.basicAirportQuery(anyListOf(String.class))).thenReturn(Collections.singletonList(new BasicAirportDTO(){{setCountryId(1L);setCityId(1L);}}));
        GetHostDestinationInfo getHostDestinationInfo1 = new GetHostDestinationInfo();
        getHostDestinationInfo1.setPickuplocationcode("CODE");

        GetHostDestinationInfo getHostDestinationInfo2 = new GetHostDestinationInfo();
        getHostDestinationInfo2.setPickuplocationcode("CODE");
        getHostDestinationInfo2.setPickupcityid("1");
        getHostDestinationInfo2.setPickupcountryid("ss");
        List<com.ctrip.car.osd.basicdataservice.dto.Airport> airports = new ArrayList<>();
        com.ctrip.car.osd.basicdataservice.dto.Airport airport = new com.ctrip.car.osd.basicdataservice.dto.Airport();
        airport.setCityId(1);
        airports.add(airport);
        when(osdBasicDataProxy.getAirportNoCityId(anyString(), anyString())).thenReturn(airports);
        seoHotDestinationSchedule.saveDestination(Collections.singletonList(getHostDestinationInfo1));
        seoHotDestinationSchedule.saveDestination(Collections.singletonList(getHostDestinationInfo2));
        Assert.assertEquals(getHostDestinationInfo1.getPickupcityid(), null);
        Assert.assertEquals(getHostDestinationInfo2.getPickupcityid(), "1");
    }


    @Test
    public void testCompleteGetSeoHotInformationData(){
        GetCarSeoRentalInfo carSeoRentalInfo1 = new GetCarSeoRentalInfo();
        carSeoRentalInfo1.setPickuplocationcode("code");
        GetCarSeoRentalInfo carSeoRentalInfo2 = new GetCarSeoRentalInfo();
        carSeoRentalInfo2.setPickuplocationcode("code2");
        List<com.ctrip.car.osd.basicdataservice.dto.Airport> airports = new ArrayList<>();
        com.ctrip.car.osd.basicdataservice.dto.Airport airport = new com.ctrip.car.osd.basicdataservice.dto.Airport();
        airport.setCityId(1);
        airports.add(airport);
        when(osdBasicDataProxy.getAirportNoCityId("CODE", "en-US")).thenReturn(airports);
        when(osdBasicDataProxy.getAirportNoCityId("CODE2", "en-US")).thenReturn(null);
        GetCarSeoRentalInfo completeGetSeoHotInformationData = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "completeGetSeoHotInformationData", carSeoRentalInfo1);
        GetCarSeoRentalInfo completeGetSeoHotInformationData2 = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "completeGetSeoHotInformationData", carSeoRentalInfo2);
        Assert.assertEquals(completeGetSeoHotInformationData.getPickcityid(), "1");
        Assert.assertNotEquals(completeGetSeoHotInformationData2.getPickcityid(), "1");
    }


    @Test
    public void testCompleteData(){
        GetHostDestinationInfo carSeoRentalInfo1 = new GetHostDestinationInfo();
        carSeoRentalInfo1.setPickuplocationcode("code");
        GetHostDestinationInfo carSeoRentalInfo2 = new GetHostDestinationInfo();
        carSeoRentalInfo2.setPickuplocationcode("code2");
        List<com.ctrip.car.osd.basicdataservice.dto.Airport> airports = new ArrayList<>();
        com.ctrip.car.osd.basicdataservice.dto.Airport airport = new com.ctrip.car.osd.basicdataservice.dto.Airport();
        airport.setCityId(1);
        airport.setAirportEName("sha");
        airports.add(airport);
        when(osdBasicDataProxy.getAirportNoCityId("CODE", "en-US")).thenReturn(airports);
        when(osdBasicDataProxy.getAirportNoCityId("CODE2", "en-US")).thenReturn(null);
        GetHostDestinationInfo completeGetSeoHotInformationData = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "completeHotDestinationData", carSeoRentalInfo1);
        GetHostDestinationInfo completeGetSeoHotInformationData2 = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "completeHotDestinationData", carSeoRentalInfo2);
        Assert.assertEquals(completeGetSeoHotInformationData.getPickuplocationname(), "sha");
        Assert.assertNotEquals(completeGetSeoHotInformationData2.getPickuplocationname(), "sha");

    }

    @Test
    public void testSaveCarSeoRentalInfo() throws SQLException {
        List<SeoHotInformation> seoHotInformationList = new ArrayList<>();
        SeoHotInformation seoHotInformation = new SeoHotInformation();
        seoHotInformation.setStatus(0);
        seoHotInformation.setTenancy(2);
        seoHotInformation.setVendorName("testvendor");
        seoHotInformation.setVendorId(1);
        seoHotInformation.setPoiType(1);
        seoHotInformation.setPoiCode("CODE");
        seoHotInformation.setVehicleGroupId(1);
        seoHotInformation.setVehicleGroupName("小型汽车");
        seoHotInformation.setCityId(1);
        seoHotInformation.setCountryId(1);
        seoHotInformationList.add(seoHotInformation);

        List<GetCarSeoRentalInfo> getCarSeoRentalInfoList = new ArrayList<>();
        GetCarSeoRentalInfo carSeoRentalInfo = new GetCarSeoRentalInfo();
        carSeoRentalInfo.setPickcountryid("1");
        carSeoRentalInfo.setPickcityid("1");
        carSeoRentalInfo.setPickcityname("shanghai");
        carSeoRentalInfo.setPickcountryname("china");
        carSeoRentalInfo.setVendorid("1");
        carSeoRentalInfo.setVendorname("ess");
        carSeoRentalInfo.setVehivlegroupid("1");
        carSeoRentalInfo.setVehivlegroupname("sda");
        carSeoRentalInfo.setCommon_period("2");
        carSeoRentalInfo.setPickuplocationcode("CODE");
        GetCarSeoRentalInfo carSeoRentalInfo2 = new GetCarSeoRentalInfo();
        carSeoRentalInfo2.setPickuplocationcode("aaaa");
        carSeoRentalInfo2.setPickcountryid("1");
        carSeoRentalInfo2.setPickcityid("1");
        getCarSeoRentalInfoList.add(carSeoRentalInfo);
        getCarSeoRentalInfoList.add(carSeoRentalInfo2);
        when(seoHotDestinationBusiness.getSeoHotInformation(false)).thenReturn(seoHotInformationList);
        when(seoHotDestinationBusiness.batchInsertIntoInformation(anyListOf(SeoHotInformation.class))).thenReturn(1);
        when(seoHotDestinationBusiness.batchUpdateInformation(anyListOf(SeoHotInformation.class))).thenReturn(1);
        List<com.ctrip.car.osd.basicdataservice.dto.Airport> airports = new ArrayList<>();
        com.ctrip.car.osd.basicdataservice.dto.Airport airport = new com.ctrip.car.osd.basicdataservice.dto.Airport();
        airport.setCityId(1);
        airports.add(airport);
        when(osdBasicDataProxy.getAirportNoCityId(anyString(), anyString())).thenReturn(airports);
        boolean b = seoHotDestinationSchedule.saveCarSeoRentalInfo(getCarSeoRentalInfoList);
        Assert.assertTrue(b);
    }

    @Test
    public void testSaveCarSeoRentalInfo2() throws SQLException {
        when(seoHotDestinationBusiness.getSeoHotInformation(false)).thenThrow(new SQLException());
        boolean b = seoHotDestinationSchedule.saveCarSeoRentalInfo(new ArrayList<>());
        Assert.assertFalse(b);
    }

    @Test
    public void tstSaveCarSeoRentalInfo3() throws SQLException {
        when(seoHotDestinationBusiness.getSeoHotInformation(false)).thenReturn(new ArrayList<>());
        boolean b = seoHotDestinationSchedule.saveCarSeoRentalInfo(new ArrayList<>());
        Assert.assertTrue(b);
    }

    @Test
    public void testJudgePoiInfoValid() throws SQLException {
        boolean result1 = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "judgePoiInfoValid", null, null, null);
        boolean result2 = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "judgePoiInfoValid", "code", "1", "1");
        boolean result3 = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "judgePoiInfoValid", "code", "c", "1");
        boolean result4 = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "judgePoiInfoValid", "code", "1", "c");
        Assert.assertFalse(result1);
        Assert.assertTrue(result2);
        Assert.assertFalse(result3);
        Assert.assertFalse(result4);
    }

    @Test
    public void testSetStatusMethod() throws SQLException {
        int result1 = ReflectionTestUtils.invokeMethod(seoHotDestinationSchedule, "setStatusMethod", 0);
        Assert.assertEquals(result1, 1);
    }

    @Test
    public void testpushCityToIbu() throws SQLException{
        List<SeoHotCityinfo> list = new ArrayList<>();
        SeoHotCityinfo seoHotCityinfo = new SeoHotCityinfo();
        seoHotCityinfo.setCityId(58);
        seoHotCityinfo.setCityName("hongkong");
        seoHotCityinfo.setStatus(1);
        seoHotCityinfo.setCountryId(1);
        seoHotCityinfo.setUrl("https://www.fat1.qa.nt.tripqate.com/carhire/to-china-1/hong-kong-58/");
        list.add(seoHotCityinfo);
        when(seoHotDestinationBusiness.getSeoHotCityinfoList(Boolean.TRUE)).thenReturn(list);
        when(cityRepository.findMany(anyListOf(Long.class))).thenReturn(null);
        seoHotDestinationSchedule.pushCityToIbu();
        Assert.assertEquals(58, (int) list.get(0).getCityId());
    }

    @Test
    public void testpushCountryToIbu() throws SQLException{
        List<SeoHotCountryinfo> list = new ArrayList<>();
        SeoHotCountryinfo seoHotCountryinfo = new SeoHotCountryinfo();
        seoHotCountryinfo.setCountryId(1);
        seoHotCountryinfo.setCountryName("china");
        seoHotCountryinfo.setStatus(1);
        seoHotCountryinfo.setUrl("https://www.fat1.qa.nt.tripqate.com/carhire/to-china-1/");
        list.add(seoHotCountryinfo);
        when(seoHotDestinationBusiness.getSeoHotCountryinfoList(Boolean.TRUE)).thenReturn(list);
        when(countryRepository.findMany(anyListOf(Long.class))).thenReturn(null);
        seoHotDestinationSchedule.pushCountryToIbu();
        Assert.assertEquals(1, (int) list.get(0).getCountryId());
    }

    @Test
    public void testPushHotProvinceToIbu() throws SQLException {
        List<SeoHotProvinceinfo> seoHotProvinceinfoList = new ArrayList<>();
        SeoHotProvinceinfo provinceInfo = new SeoHotProvinceinfo();
        provinceInfo.setProvinceId(1);
        provinceInfo.setProvinceName("Beijing");
        provinceInfo.setCountryId(1);
        provinceInfo.setUrl("https://www.trip.com/carhire/to-china-1/beijing-1/");
        provinceInfo.setStatus(1);
        seoHotProvinceinfoList.add(provinceInfo);
        when(seoHotDestinationBusiness.getSeoHotProvinceinfoList(Boolean.TRUE)).thenReturn(seoHotProvinceinfoList);
        when(seoHotDestinationBusiness.buildHotDestinationMassageDTO(any(MessageParams.class)))
                .thenReturn(Collections.singletonList(new HotDestinationMassageDTO()));
        when(messageProducer.generateMessage(anyString())).thenReturn(mock(Message.class));
        Province province = Province.builder()
                .id(1L)
                .translationName("Beijing Province")
                .build();
        Map<Long, Province> provinceMap = new HashMap<>();
        provinceMap.put(1L, province);
        when(provinceRepository.findMany(anyListOf(Long.class), anyString())).thenReturn(provinceMap);
        seoHotDestinationSchedule.pushHotProvinceToIbu();
        verify(seoHotDestinationBusiness, times(1)).getSeoHotProvinceinfoList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, times(1)).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, atLeastOnce()).generateMessage(anyString());
    }

    @Test
    public void testPushHotProvinceToIbuWithException() throws SQLException {
        when(seoHotDestinationBusiness.getSeoHotProvinceinfoList(Boolean.TRUE)).thenThrow(new SQLException());
        seoHotDestinationSchedule.pushHotProvinceToIbu();
        verify(seoHotDestinationBusiness, times(1)).getSeoHotProvinceinfoList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, never()).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, never()).generateMessage(anyString());
    }

    @Test
    public void testPushHotProvinceToIbuWithEmptyList() throws SQLException {
        when(seoHotDestinationBusiness.getSeoHotProvinceinfoList(Boolean.TRUE)).thenReturn(new ArrayList<>());
        seoHotDestinationSchedule.pushHotProvinceToIbu();
        verify(seoHotDestinationBusiness, times(1)).getSeoHotProvinceinfoList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, never()).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, never()).generateMessage(anyString());
    }

    @Test
    public void testPushTrainStationToIbu() throws SQLException {
        List<SeoHotDestinatioinfo> seoTrainStationList = new ArrayList<>();
        SeoHotDestinatioinfo trainStation = new SeoHotDestinatioinfo();
        trainStation.setPoiCode("BJN");
        trainStation.setPoiName("Beijing Railway Station");
        trainStation.setPoiId(12345L);
        trainStation.setCountryId(1);
        trainStation.setCityId(58);
        trainStation.setUrl("https://www.trip.com/carhire/to-china-1/beijing-58/beijing-railway-station-bjn/");
        trainStation.setStatus(0);
        seoTrainStationList.add(trainStation);
        
        when(seoHotDestinationBusiness.getSeoTrainStationList(Boolean.TRUE)).thenReturn(seoTrainStationList);
        when(seoHotDestinationBusiness.buildHotDestinationMassageDTO(any(MessageParams.class)))
                .thenReturn(Collections.singletonList(new HotDestinationMassageDTO()));
        when(messageProducer.generateMessage(anyString())).thenReturn(mock(Message.class));
        
        City city = City.builder()
                .id(58L)
                .provinceId(1L)
                .translationName("Beijing")
                .build();
        Map<Long, City> cityMap = new HashMap<>();
        cityMap.put(58L, city);
        when(cityRepository.findMany(anyListOf(Long.class), anyString())).thenReturn(cityMap);
        
        PlaceDetailsDTO placeDetails = new PlaceDetailsDTO();
        placeDetails.setName("Beijing Railway Station");
        when(igtGeoServiceProxy.getTrainStation("BJN", "en-ID")).thenReturn(placeDetails);
        
        seoHotDestinationSchedule.pushTrainStationToIbu();
        
        verify(seoHotDestinationBusiness, times(1)).getSeoTrainStationList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, times(1)).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, atLeastOnce()).generateMessage(anyString());
    }

    @Test
    public void testPushTrainStationToIbuWithException() throws SQLException {
        when(seoHotDestinationBusiness.getSeoTrainStationList(Boolean.TRUE)).thenThrow(new SQLException());
        seoHotDestinationSchedule.pushTrainStationToIbu();
        verify(seoHotDestinationBusiness, times(1)).getSeoTrainStationList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, never()).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, never()).generateMessage(anyString());
    }

    @Test
    public void testPushTrainStationToIbuWithEmptyList() throws SQLException {
        when(seoHotDestinationBusiness.getSeoTrainStationList(Boolean.TRUE)).thenReturn(new ArrayList<>());
        seoHotDestinationSchedule.pushTrainStationToIbu();
        verify(seoHotDestinationBusiness, times(1)).getSeoTrainStationList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, never()).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, never()).generateMessage(anyString());
    }

    @Test
    public void testPushScenerySpotToIbu() throws SQLException {
        List<SeoHotDestinatioinfo> seoScenerySpotList = new ArrayList<>();
        SeoHotDestinatioinfo scenerySpot = new SeoHotDestinatioinfo();
        scenerySpot.setPoiCode("FORBIDDEN_CITY");
        scenerySpot.setPoiName("Forbidden City");
        scenerySpot.setPoiId(67890L);
        scenerySpot.setCountryId(1);
        scenerySpot.setCityId(58);
        scenerySpot.setUrl("https://www.trip.com/carhire/to-china-1/beijing-58/forbidden-city-forbidden_city/");
        scenerySpot.setStatus(0);
        seoScenerySpotList.add(scenerySpot);
        
        when(seoHotDestinationBusiness.getSeoScenerySpotList(Boolean.TRUE)).thenReturn(seoScenerySpotList);
        when(seoHotDestinationBusiness.buildHotDestinationMassageDTO(any(MessageParams.class)))
                .thenReturn(Collections.singletonList(new HotDestinationMassageDTO()));
        when(messageProducer.generateMessage(anyString())).thenReturn(mock(Message.class));
        
        City city = City.builder()
                .id(58L)
                .provinceId(1L)
                .translationName("Beijing")
                .build();
        Map<Long, City> cityMap = new HashMap<>();
        cityMap.put(58L, city);
        when(cityRepository.findMany(anyListOf(Long.class), anyString())).thenReturn(cityMap);
        
        GlobalInfo globalInfo = new GlobalInfo();
        GlobalContent globalContent = new GlobalContent();
        globalContent.setGlobal("Forbidden City");
        globalInfo.setPoiName(globalContent);
        when(globalPoiJavaProxy.getScenerySpot("FORBIDDEN_CITY", "en-ID")).thenReturn(Collections.singletonList(globalInfo));
        
        seoHotDestinationSchedule.pushScenerySpotToIbu();
        
        verify(seoHotDestinationBusiness, times(1)).getSeoScenerySpotList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, times(1)).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, atLeastOnce()).generateMessage(anyString());
    }

    @Test
    public void testPushScenerySpotToIbuWithException() throws SQLException {
        when(seoHotDestinationBusiness.getSeoScenerySpotList(Boolean.TRUE)).thenThrow(new SQLException());
        seoHotDestinationSchedule.pushScenerySpotToIbu();
        verify(seoHotDestinationBusiness, times(1)).getSeoScenerySpotList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, never()).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, never()).generateMessage(anyString());
    }

    @Test
    public void testPushScenerySpotToIbuWithEmptyList() throws SQLException {
        when(seoHotDestinationBusiness.getSeoScenerySpotList(Boolean.TRUE)).thenReturn(new ArrayList<>());
        seoHotDestinationSchedule.pushScenerySpotToIbu();
        verify(seoHotDestinationBusiness, times(1)).getSeoScenerySpotList(Boolean.TRUE);
        verify(seoHotDestinationBusiness, never()).buildHotDestinationMassageDTO(any(MessageParams.class));
        verify(messageProducer, never()).generateMessage(anyString());
    }
    
}
